<?php

namespace pages\backoffice\api_v2\drawManagement\lender\SowCategories;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\SowTemplateManager;

/**
 * Class SowCategories
 *
 * API endpoint for updating and fetching SOW categories
 *
 * @package pages\backoffice\api_v2\DrawManagement
 */
class SowCategories extends BackofficePage
{
    /**
     * Handle GET requests to fetch SOW categories
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $pcId = $_GET['pcid'];
        if ($pcId && !is_numeric($pcId)) $pcId = cypher::decrypt($pcId);
        $pcId = (int)$pcId;

        if (!$pcId) {
            HTTP::ExitJSON(["success" => false, "message" => "PCID is required."]);
            return;
        }

        try {
            $sowTemplateManager = SowTemplateManager::forProcessingCompany($pcId);
            $sowCategories = $sowTemplateManager->getTemplateDataArray();
            if (empty($sowCategories)) {
                HTTP::ExitJSON(["success" => true, "message" => "No categories found.", "data" => []]);
            }
            HTTP::ExitJSON(["success" => true, "message" => "Categories fetched successfully.", "data" => $sowCategories]);
        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }

    /**
     * Handle POST requests to update SOW categories
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        $pcId = $postData['pcid'];
        if ($pcId && !is_numeric($pcId)) $pcId = cypher::decrypt($pcId);
        $pcId = (int)$pcId;

        $categoriesData = $postData['categories'] ?? [];

        if (!$pcId) {
            HTTP::ExitJSON(["success" => false, "message" => "PCID is required."]);
            return;
        }

        try {
            $sowTemplateManager = SowTemplateManager::forProcessingCompany($pcId);
            $result = $sowTemplateManager->saveCategories($categoriesData);

            if (!$result) {
                HTTP::ExitJSON(["success" => false, "message" => "Failed to save categories."]);
            }
            $categoriesData = $sowTemplateManager->getTemplateDataArray();
            HTTP::ExitJSON(["success" => true, "message" => "Categories saved successfully.", "data" => $categoriesData]);
        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }
}
